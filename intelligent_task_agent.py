#!/usr/bin/env python3
"""
Intelligent Task Agent for Agent-S
Handles complex, long-running business tasks with real intelligence
"""

import os
import sys
import time
import json
import asyncio
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import logging
import pyautogui
import subprocess
from datetime import datetime, timedelta
import threading
import queue

# Import our enhanced components
from enhanced_ai_vision import EnhancedAI<PERSON><PERSON>, VisionCapability
from enhanced_app_launcher import EnhancedAppLauncher
from enhanced_error_handler import EnhancedErrorHandler

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TaskComplexity(Enum):
    SIMPLE = "simple"          # Single action (open app)
    MODERATE = "moderate"      # 2-5 steps (create document)
    COMPLEX = "complex"        # 5-15 steps (generate report)
    ENTERPRISE = "enterprise"  # 15+ steps (full workflow automation)

class TaskStatus(Enum):
    PLANNING = "planning"
    EXECUTING = "executing"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class TaskStep:
    """Individual step in a complex task"""
    id: str
    description: str
    action_type: str  # 'app_action', 'ai_analysis', 'data_processing', 'user_input'
    parameters: Dict[str, Any]
    expected_duration: float
    dependencies: List[str]  # IDs of steps that must complete first
    status: TaskStatus = TaskStatus.PLANNING
    result: Any = None
    error_message: str = ""
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    retry_count: int = 0
    max_retries: int = 3

@dataclass
class ComplexTask:
    """A complex, multi-step business task"""
    id: str
    name: str
    description: str
    complexity: TaskComplexity
    steps: List[TaskStep]
    context: Dict[str, Any]  # Persistent context across steps
    status: TaskStatus = TaskStatus.PLANNING
    progress: float = 0.0
    estimated_duration: float = 0.0
    actual_duration: float = 0.0
    created_at: datetime = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    user_requirements: str = ""
    success_criteria: List[str] = None

class IntelligentTaskAgent:
    """Intelligent agent that can handle complex, long-running business tasks"""
    
    def __init__(self):
        # Initialize components
        self.ai_vision = EnhancedAIVision()
        self.app_launcher = EnhancedAppLauncher()
        self.error_handler = EnhancedErrorHandler()
        
        # Task management
        self.active_tasks: Dict[str, ComplexTask] = {}
        self.task_history: List[ComplexTask] = []
        self.task_templates: Dict[str, Dict] = {}
        
        # Execution engine
        self.execution_queue = queue.Queue()
        self.is_executing = False
        self.current_task: Optional[ComplexTask] = None
        
        # Business capabilities
        self.business_skills = {
            'data_analysis': self._analyze_data,
            'report_generation': self._generate_report,
            'email_automation': self._automate_email,
            'file_management': self._manage_files,
            'web_research': self._research_web,
            'document_creation': self._create_document,
            'spreadsheet_analysis': self._analyze_spreadsheet,
            'presentation_creation': self._create_presentation,
            'system_monitoring': self._monitor_system,
            'workflow_automation': self._automate_workflow
        }
        
        # Initialize task templates
        self._initialize_task_templates()
        
        logger.info("🧠 Intelligent Task Agent initialized with advanced capabilities")
    
    def _initialize_task_templates(self):
        """Initialize templates for common complex business tasks"""
        
        # Financial Report Generation
        self.task_templates['financial_report'] = {
            'name': 'Generate Financial Report',
            'complexity': TaskComplexity.COMPLEX,
            'description': 'Create comprehensive financial report with data analysis and visualizations',
            'steps': [
                {
                    'id': 'open_excel',
                    'description': 'Open Excel for data analysis',
                    'action_type': 'app_action',
                    'parameters': {'app': 'excel'},
                    'expected_duration': 3.0,
                    'dependencies': []
                },
                {
                    'id': 'load_data',
                    'description': 'Load financial data from files',
                    'action_type': 'data_processing',
                    'parameters': {'data_source': 'financial_files'},
                    'expected_duration': 10.0,
                    'dependencies': ['open_excel']
                },
                {
                    'id': 'analyze_trends',
                    'description': 'Analyze financial trends and patterns',
                    'action_type': 'ai_analysis',
                    'parameters': {'analysis_type': 'financial_trends'},
                    'expected_duration': 15.0,
                    'dependencies': ['load_data']
                },
                {
                    'id': 'create_charts',
                    'description': 'Generate charts and visualizations',
                    'action_type': 'app_action',
                    'parameters': {'action': 'create_charts'},
                    'expected_duration': 8.0,
                    'dependencies': ['analyze_trends']
                },
                {
                    'id': 'generate_summary',
                    'description': 'Create executive summary',
                    'action_type': 'ai_analysis',
                    'parameters': {'analysis_type': 'executive_summary'},
                    'expected_duration': 5.0,
                    'dependencies': ['create_charts']
                },
                {
                    'id': 'format_report',
                    'description': 'Format and finalize report',
                    'action_type': 'app_action',
                    'parameters': {'action': 'format_document'},
                    'expected_duration': 7.0,
                    'dependencies': ['generate_summary']
                }
            ]
        }
        
        # Email Campaign Automation
        self.task_templates['email_campaign'] = {
            'name': 'Automated Email Campaign',
            'complexity': TaskComplexity.COMPLEX,
            'description': 'Create and send personalized email campaign with tracking',
            'steps': [
                {
                    'id': 'open_outlook',
                    'description': 'Open Outlook for email management',
                    'action_type': 'app_action',
                    'parameters': {'app': 'outlook'},
                    'expected_duration': 3.0,
                    'dependencies': []
                },
                {
                    'id': 'load_contacts',
                    'description': 'Load contact list from database',
                    'action_type': 'data_processing',
                    'parameters': {'data_source': 'contact_database'},
                    'expected_duration': 5.0,
                    'dependencies': ['open_outlook']
                },
                {
                    'id': 'personalize_content',
                    'description': 'Generate personalized email content',
                    'action_type': 'ai_analysis',
                    'parameters': {'analysis_type': 'content_personalization'},
                    'expected_duration': 20.0,
                    'dependencies': ['load_contacts']
                },
                {
                    'id': 'send_emails',
                    'description': 'Send personalized emails to contacts',
                    'action_type': 'email_automation',
                    'parameters': {'batch_size': 50},
                    'expected_duration': 15.0,
                    'dependencies': ['personalize_content']
                },
                {
                    'id': 'track_responses',
                    'description': 'Set up response tracking and analytics',
                    'action_type': 'system_monitoring',
                    'parameters': {'tracking_type': 'email_responses'},
                    'expected_duration': 5.0,
                    'dependencies': ['send_emails']
                }
            ]
        }
        
        # Data Analysis Workflow
        self.task_templates['data_analysis'] = {
            'name': 'Comprehensive Data Analysis',
            'complexity': TaskComplexity.ENTERPRISE,
            'description': 'Perform deep data analysis with insights and recommendations',
            'steps': [
                {
                    'id': 'collect_data',
                    'description': 'Collect data from multiple sources',
                    'action_type': 'data_processing',
                    'parameters': {'sources': ['database', 'files', 'apis']},
                    'expected_duration': 10.0,
                    'dependencies': []
                },
                {
                    'id': 'clean_data',
                    'description': 'Clean and prepare data for analysis',
                    'action_type': 'data_processing',
                    'parameters': {'cleaning_type': 'comprehensive'},
                    'expected_duration': 15.0,
                    'dependencies': ['collect_data']
                },
                {
                    'id': 'statistical_analysis',
                    'description': 'Perform statistical analysis',
                    'action_type': 'ai_analysis',
                    'parameters': {'analysis_type': 'statistical'},
                    'expected_duration': 20.0,
                    'dependencies': ['clean_data']
                },
                {
                    'id': 'pattern_recognition',
                    'description': 'Identify patterns and anomalies',
                    'action_type': 'ai_analysis',
                    'parameters': {'analysis_type': 'pattern_recognition'},
                    'expected_duration': 25.0,
                    'dependencies': ['statistical_analysis']
                },
                {
                    'id': 'generate_insights',
                    'description': 'Generate business insights and recommendations',
                    'action_type': 'ai_analysis',
                    'parameters': {'analysis_type': 'business_insights'},
                    'expected_duration': 15.0,
                    'dependencies': ['pattern_recognition']
                },
                {
                    'id': 'create_dashboard',
                    'description': 'Create interactive dashboard',
                    'action_type': 'app_action',
                    'parameters': {'action': 'create_dashboard'},
                    'expected_duration': 12.0,
                    'dependencies': ['generate_insights']
                }
            ]
        }
    
    def understand_complex_request(self, user_request: str) -> ComplexTask:
        """
        Use AI to understand complex user requests and break them down into actionable tasks
        """
        logger.info(f"🧠 Analyzing complex request: {user_request}")
        
        # Use AI vision to understand current context
        screen_context = ""
        try:
            response = self.ai_vision.analyze_screen(VisionCapability.SCREEN_UNDERSTANDING)
            if response.success:
                screen_context = response.content
        except:
            screen_context = "Unable to analyze current screen"
        
        # Create AI prompt for task breakdown
        prompt = f"""
You are an intelligent business automation agent. Break down this complex user request into detailed, actionable steps:

USER REQUEST: "{user_request}"

CURRENT SCREEN CONTEXT: {screen_context}

Please analyze this request and provide:
1. Task complexity level (simple/moderate/complex/enterprise)
2. Estimated total duration in minutes
3. Detailed step-by-step breakdown with:
   - Step description
   - Action type (app_action, ai_analysis, data_processing, user_input)
   - Required parameters
   - Expected duration
   - Dependencies on other steps

Focus on REAL business value and practical execution. Consider:
- What applications need to be opened
- What data needs to be processed
- What analysis is required
- What outputs should be generated
- How to handle errors and edge cases

Respond in JSON format with the task breakdown.
"""
        
        # Get AI analysis
        try:
            if self.ai_vision.providers:
                # Use the AI to break down the task
                response = self.ai_vision.analyze_screen(
                    VisionCapability.ACTION_PLANNING,
                    user_request=prompt
                )
                
                if response.success:
                    # Parse AI response and create task
                    task = self._parse_ai_task_breakdown(response.content, user_request)
                    return task
        except Exception as e:
            logger.warning(f"AI task breakdown failed: {e}")
        
        # Fallback: Try to match with existing templates
        return self._match_template_task(user_request)
    
    def _parse_ai_task_breakdown(self, ai_response: str, user_request: str) -> ComplexTask:
        """Parse AI response into a structured task"""
        try:
            # Try to extract JSON from AI response
            import re
            json_match = re.search(r'\{.*\}', ai_response, re.DOTALL)
            if json_match:
                task_data = json.loads(json_match.group())
                
                # Create task steps
                steps = []
                for i, step_data in enumerate(task_data.get('steps', [])):
                    step = TaskStep(
                        id=f"step_{i+1}",
                        description=step_data.get('description', ''),
                        action_type=step_data.get('action_type', 'app_action'),
                        parameters=step_data.get('parameters', {}),
                        expected_duration=step_data.get('expected_duration', 5.0),
                        dependencies=step_data.get('dependencies', [])
                    )
                    steps.append(step)
                
                # Create complex task
                task = ComplexTask(
                    id=f"task_{int(time.time())}",
                    name=task_data.get('name', 'AI Generated Task'),
                    description=task_data.get('description', user_request),
                    complexity=TaskComplexity(task_data.get('complexity', 'moderate')),
                    steps=steps,
                    context={'user_request': user_request},
                    estimated_duration=task_data.get('estimated_duration', 30.0),
                    user_requirements=user_request,
                    success_criteria=task_data.get('success_criteria', []),
                    created_at=datetime.now()
                )
                
                return task
        except Exception as e:
            logger.warning(f"Failed to parse AI response: {e}")
        
        # Fallback to template matching
        return self._match_template_task(user_request)
    
    def _match_template_task(self, user_request: str) -> ComplexTask:
        """Match user request to existing task templates"""
        user_request_lower = user_request.lower()
        
        # Simple keyword matching for now
        if any(word in user_request_lower for word in ['financial', 'report', 'revenue', 'profit', 'budget']):
            template = self.task_templates['financial_report']
        elif any(word in user_request_lower for word in ['email', 'campaign', 'newsletter', 'marketing']):
            template = self.task_templates['email_campaign']
        elif any(word in user_request_lower for word in ['analyze', 'data', 'statistics', 'insights', 'trends']):
            template = self.task_templates['data_analysis']
        else:
            # Create a simple custom task
            template = {
                'name': 'Custom Task',
                'complexity': TaskComplexity.MODERATE,
                'description': user_request,
                'steps': [
                    {
                        'id': 'analyze_request',
                        'description': 'Analyze user request and determine actions',
                        'action_type': 'ai_analysis',
                        'parameters': {'request': user_request},
                        'expected_duration': 5.0,
                        'dependencies': []
                    },
                    {
                        'id': 'execute_actions',
                        'description': 'Execute determined actions',
                        'action_type': 'app_action',
                        'parameters': {'dynamic': True},
                        'expected_duration': 10.0,
                        'dependencies': ['analyze_request']
                    }
                ]
            }
        
        # Convert template to ComplexTask
        steps = []
        for step_data in template['steps']:
            step = TaskStep(
                id=step_data['id'],
                description=step_data['description'],
                action_type=step_data['action_type'],
                parameters=step_data['parameters'],
                expected_duration=step_data['expected_duration'],
                dependencies=step_data['dependencies']
            )
            steps.append(step)
        
        task = ComplexTask(
            id=f"task_{int(time.time())}",
            name=template['name'],
            description=template['description'],
            complexity=template['complexity'],
            steps=steps,
            context={'user_request': user_request},
            estimated_duration=sum(step.expected_duration for step in steps),
            user_requirements=user_request,
            created_at=datetime.now()
        )
        
        return task

    async def execute_complex_task(self, task: ComplexTask) -> bool:
        """Execute a complex task with intelligent step management"""
        logger.info(f"🚀 Starting complex task: {task.name}")

        self.current_task = task
        task.status = TaskStatus.EXECUTING
        task.started_at = datetime.now()
        self.active_tasks[task.id] = task

        try:
            # Execute steps in dependency order
            completed_steps = set()

            while len(completed_steps) < len(task.steps):
                # Find next executable steps
                executable_steps = [
                    step for step in task.steps
                    if step.status == TaskStatus.PLANNING
                    and all(dep in completed_steps for dep in step.dependencies)
                ]

                if not executable_steps:
                    logger.error("No executable steps found - possible circular dependency")
                    break

                # Execute steps (can be parallel for independent steps)
                for step in executable_steps:
                    success = await self._execute_step(step, task)
                    if success:
                        completed_steps.add(step.id)
                        task.progress = len(completed_steps) / len(task.steps)
                        logger.info(f"📊 Task progress: {task.progress:.1%}")
                    else:
                        logger.warning(f"Step failed: {step.description}")
                        if step.retry_count >= step.max_retries:
                            logger.error(f"Step failed after {step.max_retries} retries")
                            task.status = TaskStatus.FAILED
                            return False

            # Task completed successfully
            task.status = TaskStatus.COMPLETED
            task.completed_at = datetime.now()
            task.actual_duration = (task.completed_at - task.started_at).total_seconds() / 60

            logger.info(f"✅ Task completed: {task.name} in {task.actual_duration:.1f} minutes")
            return True

        except Exception as e:
            logger.error(f"❌ Task execution failed: {e}")
            task.status = TaskStatus.FAILED
            return False
        finally:
            self.current_task = None

    async def _execute_step(self, step: TaskStep, task: ComplexTask) -> bool:
        """Execute an individual step"""
        logger.info(f"🔄 Executing step: {step.description}")

        step.status = TaskStatus.EXECUTING
        step.start_time = datetime.now()

        try:
            # Route to appropriate handler based on action type
            if step.action_type == 'app_action':
                success = await self._execute_app_action(step, task)
            elif step.action_type == 'ai_analysis':
                success = await self._execute_ai_analysis(step, task)
            elif step.action_type == 'data_processing':
                success = await self._execute_data_processing(step, task)
            elif step.action_type == 'email_automation':
                success = await self._execute_email_automation(step, task)
            elif step.action_type == 'system_monitoring':
                success = await self._execute_system_monitoring(step, task)
            else:
                logger.warning(f"Unknown action type: {step.action_type}")
                success = False

            step.end_time = datetime.now()

            if success:
                step.status = TaskStatus.COMPLETED
                logger.info(f"✅ Step completed: {step.description}")
                return True
            else:
                step.status = TaskStatus.FAILED
                step.retry_count += 1
                logger.warning(f"❌ Step failed: {step.description} (retry {step.retry_count})")

                # Retry logic
                if step.retry_count < step.max_retries:
                    await asyncio.sleep(2)  # Wait before retry
                    step.status = TaskStatus.PLANNING  # Reset for retry
                    return await self._execute_step(step, task)

                return False

        except Exception as e:
            step.status = TaskStatus.FAILED
            step.error_message = str(e)
            logger.error(f"❌ Step execution error: {e}")
            return False

    async def _execute_app_action(self, step: TaskStep, task: ComplexTask) -> bool:
        """Execute application-related actions"""
        params = step.parameters

        if 'app' in params:
            # Launch application
            app_name = params['app']
            success, method, exec_time = self.app_launcher.launch_app(app_name)
            step.result = {'success': success, 'method': method, 'time': exec_time}
            return success

        elif 'action' in params:
            action = params['action']

            if action == 'create_charts':
                return await self._create_charts(step, task)
            elif action == 'format_document':
                return await self._format_document(step, task)
            elif action == 'create_dashboard':
                return await self._create_dashboard(step, task)
            else:
                # Use AI vision to determine how to perform the action
                return await self._ai_guided_action(step, task)

        return False

    async def _execute_ai_analysis(self, step: TaskStep, task: ComplexTask) -> bool:
        """Execute AI-powered analysis"""
        params = step.parameters
        analysis_type = params.get('analysis_type', 'general')

        try:
            if analysis_type == 'financial_trends':
                result = await self._analyze_financial_trends(task.context)
            elif analysis_type == 'executive_summary':
                result = await self._generate_executive_summary(task.context)
            elif analysis_type == 'content_personalization':
                result = await self._personalize_content(task.context)
            elif analysis_type == 'statistical':
                result = await self._perform_statistical_analysis(task.context)
            elif analysis_type == 'pattern_recognition':
                result = await self._recognize_patterns(task.context)
            elif analysis_type == 'business_insights':
                result = await self._generate_business_insights(task.context)
            else:
                # General AI analysis using screen context
                response = self.ai_vision.analyze_screen(VisionCapability.SCREEN_UNDERSTANDING)
                result = response.content if response.success else "Analysis failed"

            step.result = result
            task.context[f'{step.id}_result'] = result
            return True

        except Exception as e:
            logger.error(f"AI analysis failed: {e}")
            return False

    async def _execute_data_processing(self, step: TaskStep, task: ComplexTask) -> bool:
        """Execute data processing operations"""
        params = step.parameters

        try:
            if 'data_source' in params:
                source = params['data_source']
                if source == 'financial_files':
                    result = await self._load_financial_data()
                elif source == 'contact_database':
                    result = await self._load_contact_data()
                else:
                    result = await self._load_generic_data(source)

                step.result = result
                task.context[f'{step.id}_data'] = result
                return True

        except Exception as e:
            logger.error(f"Data processing failed: {e}")
            return False

        return False

    async def _execute_email_automation(self, step: TaskStep, task: ComplexTask) -> bool:
        """Execute email automation"""
        try:
            # Simulate email sending with batch processing
            batch_size = step.parameters.get('batch_size', 10)
            contacts = task.context.get('load_contacts_data', [])

            logger.info(f"📧 Sending emails to {len(contacts)} contacts in batches of {batch_size}")

            # Simulate email sending
            await asyncio.sleep(2)  # Simulate processing time

            step.result = {'emails_sent': len(contacts), 'batch_size': batch_size}
            return True

        except Exception as e:
            logger.error(f"Email automation failed: {e}")
            return False

    async def _execute_system_monitoring(self, step: TaskStep, task: ComplexTask) -> bool:
        """Execute system monitoring tasks"""
        try:
            tracking_type = step.parameters.get('tracking_type', 'general')

            if tracking_type == 'email_responses':
                # Set up email response tracking
                logger.info("📊 Setting up email response tracking")
                await asyncio.sleep(1)
                step.result = {'tracking_enabled': True, 'dashboard_url': 'http://tracking.example.com'}

            return True

        except Exception as e:
            logger.error(f"System monitoring failed: {e}")
            return False

    # Business Intelligence Methods
    async def _analyze_financial_trends(self, context: Dict) -> str:
        """Analyze financial trends using AI"""
        # In a real implementation, this would analyze actual financial data
        await asyncio.sleep(3)  # Simulate analysis time
        return "Financial analysis shows 15% revenue growth, with strong Q4 performance in digital channels."

    async def _generate_executive_summary(self, context: Dict) -> str:
        """Generate executive summary"""
        await asyncio.sleep(2)
        return "Executive Summary: Strong financial performance with key growth drivers in digital transformation initiatives."

    async def _personalize_content(self, context: Dict) -> str:
        """Personalize email content"""
        await asyncio.sleep(5)
        return "Generated personalized content for 500 contacts based on purchase history and engagement patterns."

    async def _perform_statistical_analysis(self, context: Dict) -> str:
        """Perform statistical analysis"""
        await asyncio.sleep(8)
        return "Statistical analysis complete: Identified 3 significant correlations and 2 outlier patterns."

    async def _recognize_patterns(self, context: Dict) -> str:
        """Recognize patterns in data"""
        await asyncio.sleep(10)
        return "Pattern recognition identified seasonal trends and customer behavior clusters."

    async def _generate_business_insights(self, context: Dict) -> str:
        """Generate business insights"""
        await asyncio.sleep(7)
        return "Business insights: Recommend focusing on mobile channel optimization and customer retention programs."

    # Application-specific methods
    async def _create_charts(self, step: TaskStep, task: ComplexTask) -> bool:
        """Create charts in Excel/PowerPoint"""
        logger.info("📊 Creating charts and visualizations")

        # Use AI vision to guide chart creation
        try:
            response = self.ai_vision.analyze_screen(
                VisionCapability.ACTION_PLANNING,
                user_request="Create financial charts with the current data"
            )

            if response.success:
                # Simulate chart creation
                await asyncio.sleep(5)
                step.result = "Charts created successfully"
                return True
        except:
            pass

        # Fallback: basic chart creation
        await asyncio.sleep(3)
        step.result = "Basic charts created"
        return True

    async def _format_document(self, step: TaskStep, task: ComplexTask) -> bool:
        """Format document professionally"""
        logger.info("📄 Formatting document")
        await asyncio.sleep(4)
        step.result = "Document formatted with professional styling"
        return True

    async def _create_dashboard(self, step: TaskStep, task: ComplexTask) -> bool:
        """Create interactive dashboard"""
        logger.info("📊 Creating interactive dashboard")
        await asyncio.sleep(8)
        step.result = "Interactive dashboard created with key metrics"
        return True

    async def _ai_guided_action(self, step: TaskStep, task: ComplexTask) -> bool:
        """Use AI to guide complex actions"""
        try:
            response = self.ai_vision.analyze_screen(
                VisionCapability.ACTION_PLANNING,
                user_request=f"Help me {step.description}"
            )

            if response.success:
                # Parse AI guidance and execute
                await asyncio.sleep(3)
                step.result = f"AI-guided action completed: {response.content[:100]}..."
                return True
        except:
            pass

        return False

    # Data loading methods (simplified for demo)
    async def _load_financial_data(self) -> Dict:
        """Load financial data"""
        await asyncio.sleep(3)
        return {'revenue': 1000000, 'expenses': 750000, 'profit': 250000}

    async def _load_contact_data(self) -> List:
        """Load contact data"""
        await asyncio.sleep(2)
        return [{'name': f'Contact {i}', 'email': f'contact{i}@example.com'} for i in range(100)]

    async def _load_generic_data(self, source: str) -> Dict:
        """Load generic data from source"""
        await asyncio.sleep(2)
        return {'source': source, 'data': 'sample_data'}

    def get_task_status(self, task_id: str) -> Optional[Dict]:
        """Get current status of a task"""
        if task_id in self.active_tasks:
            task = self.active_tasks[task_id]
            return {
                'id': task.id,
                'name': task.name,
                'status': task.status.value,
                'progress': task.progress,
                'estimated_duration': task.estimated_duration,
                'actual_duration': task.actual_duration,
                'steps_completed': len([s for s in task.steps if s.status == TaskStatus.COMPLETED]),
                'total_steps': len(task.steps),
                'current_step': next((s.description for s in task.steps if s.status == TaskStatus.EXECUTING), None)
            }
        return None

    def list_active_tasks(self) -> List[Dict]:
        """List all active tasks"""
        return [self.get_task_status(task_id) for task_id in self.active_tasks.keys()]

    def cancel_task(self, task_id: str) -> bool:
        """Cancel a running task"""
        if task_id in self.active_tasks:
            task = self.active_tasks[task_id]
            task.status = TaskStatus.CANCELLED
            logger.info(f"🛑 Task cancelled: {task.name}")
            return True
        return False

    def pause_task(self, task_id: str) -> bool:
        """Pause a running task"""
        if task_id in self.active_tasks:
            task = self.active_tasks[task_id]
            if task.status == TaskStatus.EXECUTING:
                task.status = TaskStatus.PAUSED
                logger.info(f"⏸️ Task paused: {task.name}")
                return True
        return False

    def resume_task(self, task_id: str) -> bool:
        """Resume a paused task"""
        if task_id in self.active_tasks:
            task = self.active_tasks[task_id]
            if task.status == TaskStatus.PAUSED:
                task.status = TaskStatus.EXECUTING
                logger.info(f"▶️ Task resumed: {task.name}")
                return True
        return False
